import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../design_system/app_theme.dart';
import '../../electrical_components/transformer_trainer/jj_transformer_trainer.dart';
import '../../electrical_components/transformer_trainer/models/transformer_models.dart';

class TransformerTrainingScreen extends StatefulWidget {
  const TransformerTrainingScreen({super.key});

  @override
  State<TransformerTrainingScreen> createState() => _TransformerTrainingScreenState();
}

class _TransformerTrainingScreenState extends State<TransformerTrainingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightGray,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryNavy,
        title: Text(
          'Transformer Bank Training',
          style: AppTheme.headlineMedium.copyWith(
            color: AppTheme.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: AppTheme.white),
        elevation: 0,
      ),
      body: Container(
        color: AppTheme.lightGray,
        child: JJTransformerTrainer(
          onBankComplete: _handleBankCompletion,
          onError: _handleError,
        ),
      ),
    );
  }

  void _handleBankCompletion(TransformerBankType bankType) {
    // Show completion feedback with electrical theme
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: AppTheme.primaryNavy,
        content: Row(
          children: [
            Icon(
              Icons.electrical_services,
              color: AppTheme.accentCopper,
              size: AppTheme.iconSm,
            ),
            const SizedBox(width: AppTheme.spacingSm),
            Expanded(
              child: Text(
                'Great work! You completed the ${bankType.name} transformer bank.',
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.white),
              ),
            ),
          ],
        ),
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Continue',
          textColor: AppTheme.accentCopper,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  void _handleError(String error) {
    // Show error with electrical theme
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Colors.red.shade700,
        content: Row(
          children: [
            const Icon(
              Icons.warning,
              color: AppTheme.white,
              size: AppTheme.iconSm,
            ),
            const SizedBox(width: AppTheme.spacingSm),
            Expanded(
              child: Text(
                error,
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.white),
              ),
            ),
          ],
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
