
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/transformer_models.dart';
import '../state/transformer_state.dart';
import '../widgets/transformer_diagram.dart';
import '../models/educational_content.dart';

/// Quiz mode for testing knowledge without guidance
class QuizModeWidget extends StatelessWidget {
  final Function(TrainingStep)? onStepComplete;
  final Function(TransformerBankType)? onBankComplete;
  final Function(String)? onError;

  const QuizModeWidget({
    super.key,
    this.onStepComplete,
    this.onBankComplete,
    this.onError,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<TransformerTrainerState>(
      builder: (context, state, child) {
        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Column(
                  children: [
                    // Quiz header with bank info
                    _buildQuizHeader(state),
                    
                    // Connection status
                    _buildConnectionStatus(state),
                    
                    // Main transformer diagram with fixed height
                    SizedBox(
                      height: 450,
                      child: TransformerDiagram(
                        onConnectionMade: (fromId, toId) {
                          state.addConnection(fromId, toId);
                          
                          // Check if bank was completed
                          if (state.currentState.isComplete && onBankComplete != null) {
                            onBankComplete!(state.currentState.bankType);
                            _showCompletionDialog(context, state);
                          }
                        },
                        onConnectionError: (error) {
                          if (onError != null) {
                            onError!(error);
                          }
                          _showErrorFeedback(context, error);
                        },
                        showGuidance: false, // No guidance in quiz mode
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Quiz control buttons - keep at bottom
            _buildControlButtons(context, state),
          ],
        );
      },
    );
  }

  /// Build quiz header with bank information
  Widget _buildQuizHeader(TransformerTrainerState state) {
    final bankType = state.currentState.bankType;
    final title = EducationalContent.getBankTitle(bankType);
    
    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple[100]!, Colors.purple[50]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.quiz, color: Colors.purple[700], size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Quiz Mode',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.purple[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.purple[50],
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.purple[200]!),
            ),
            child: const Row(
              children: [
                Icon(Icons.info_outline, color: Colors.purple),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Make all the correct connections to complete this transformer bank configuration. No guidance will be provided.',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build connection status indicator
  Widget _buildConnectionStatus(TransformerTrainerState state) {
    final totalConnections = state.requiredConnections.length;
    final correctConnections = state.currentState.connections
        .where((conn) => conn.isCorrect)
        .length;
    final incorrectConnections = state.currentState.connections
        .where((conn) => !conn.isCorrect)
        .length;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatusItem(
                  'Correct',
                  correctConnections,
                  Colors.green,
                  Icons.check_circle,
                ),
              ),
              Expanded(
                child: _buildStatusItem(
                  'Incorrect',
                  incorrectConnections,
                  Colors.red,
                  Icons.cancel,
                ),
              ),
              Expanded(
                child: _buildStatusItem(
                  'Remaining',
                  totalConnections - correctConnections,
                  Colors.orange,
                  Icons.radio_button_unchecked,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: totalConnections > 0 ? correctConnections / totalConnections : 0,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
          ),
          const SizedBox(height: 4),
          Text(
            '$correctConnections of $totalConnections connections completed',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual status item
  Widget _buildStatusItem(String label, int count, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build control buttons for quiz mode
  Widget _buildControlButtons(BuildContext context, TransformerTrainerState state) {
    final hasConnections = state.currentState.connections.isNotEmpty;
    final isComplete = state.currentState.isComplete;
    
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Clear all button
          Expanded(
            child: OutlinedButton.icon(
              onPressed: hasConnections
                  ? () {
                      _showClearConfirmation(context, state);
                    }
                  : null,
              icon: const Icon(Icons.clear_all),
              label: const Text('Clear All'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: BorderSide(color: Colors.red[300]!),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Check answers button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: hasConnections
                  ? () {
                      _showAnswerCheck(context, state);
                    }
                  : null,
              icon: const Icon(Icons.fact_check),
              label: const Text('Check Answers'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Submit/Complete button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: isComplete
                  ? () {
                      _showCompletionDialog(context, state);
                    }
                  : null,
              icon: const Icon(Icons.check),
              label: const Text('Submit'),
              style: ElevatedButton.styleFrom(
                backgroundColor: isComplete ? Colors.green : Colors.grey,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show clear confirmation dialog
  void _showClearConfirmation(BuildContext context, TransformerTrainerState state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Connections?'),
        content: const Text(
          'This will remove all current connections. Are you sure you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              state.clearConnections();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  /// Show answer check dialog
  void _showAnswerCheck(BuildContext context, TransformerTrainerState state) {
    final correctConnections = state.currentState.connections
        .where((conn) => conn.isCorrect)
        .length;
    final totalRequired = state.requiredConnections.length;
    final incorrectConnections = state.currentState.connections
        .where((conn) => !conn.isCorrect)
        .toList();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Answer Check'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Correct connections: $correctConnections / $totalRequired'),
              if (incorrectConnections.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text(
                  'Incorrect connections:',
                  style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
                ),
                ...incorrectConnections.map(
                  (conn) => Padding(
                    padding: const EdgeInsets.only(top: 4.0, left: 8.0),
                    child: Text(
                      '• ${conn.fromPointId} → ${conn.toPointId}',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                if (incorrectConnections.first.errorReason != null)
                  Text(
                    'Tip: ${incorrectConnections.first.errorReason}',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show error feedback as snackbar
  void _showErrorFeedback(BuildContext context, String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(error)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Show completion dialog
  void _showCompletionDialog(BuildContext context, TransformerTrainerState state) {
    final bankType = state.currentState.bankType;
    final title = EducationalContent.getBankTitle(bankType);
    final correctConnections = state.currentState.connections
        .where((conn) => conn.isCorrect)
        .length;
    final totalConnections = state.requiredConnections.length;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.emoji_events, color: Colors.amber, size: 28),
            SizedBox(width: 8),
            Text('Quiz Complete!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Congratulations! You have successfully completed the $title quiz.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                children: [
                  const Text(
                    'Final Score',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$correctConnections / $totalConnections',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const Text('Correct Connections'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              state.clearConnections();
            },
            child: const Text('Try Again'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Finish'),
          ),
        ],
      ),
    );
  }
}
