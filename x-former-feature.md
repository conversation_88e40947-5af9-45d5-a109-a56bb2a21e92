# Transofmer Feature

*This feature is to be used as both a reference and a training tool. The user can use it to learn about transformers, and then use it to test their knowledge.*

## Overview

* There will be two main sections to this feature. A `Reference` section and a `Training` section.
* There will be a settings screen where the user can select the level of difficulty and then the configuration of the transformer bank.
* The user will be able to select different voltages and current ratings for the transformers.
* In the `Reference` section, the user will be able to tap the different parts of the transformer and learn about them.
* In the `Training` section, the user will be able to test their knowledge by trying to build the transformer bank correctly with a drag and drop interface or by using their finger to draw the connections.

## Reference

There needs to be a 'Transformer Bank' option added to the `Reference` section of the settings screen.

Once selected, the user is taken to a new screen where they can select the level of difficulty and then a certain configuration of a transformer bank. The different types of banks to be selected are as follows:

* **Transformer Configurations**

* `Single Pot`
* `Two Pot Banks`
* `Three Pot Banks`

* **Single Pot**
* 120v/240v

* **Two Pot Banks**
* `Open-Delta`

* **Three Pot Banks**
* `Wye/Wye`
* `Delta/Delta`
* `Wye/Delta`
* `Delta/Wye`

When the user selects a certain configuration, that correct and completed configuration is to render. It needs to be interactive. The user should be able to hover over the different parts of the transformer and see the name of the part. They should also be able to tap on the part and see a description of the part.

## Training

This is where the user selects the level of difficulty and then the configuration of the transformer bank.

* **Difficulty Levels**
* `Easy`
* `Medium`
* `Hard`

* **Single Pot**
* 120v/240v

* **Two Pot Banks**
* `Open-Delta`

* **Three Pot Banks**
* `Wye/Wye`
* `Delta/Delta`
* `Wye/Delta`
* `Delta/Wye`

Third, There is supposed to be an option that the user can interact with the transformer's secondary hook-ups. Like a drag and drop, or sticky keys type thing where they first select the wire, the wire is then highlighted, then they select which secondary bushing to connect the wire to.

Fourth, There should also be special animations. For example if the user connects the secondary leads in the wrong position then when the user says their complete and energizes the transformer, there will be a big electrical fire, if the connection is correct then there isnt a fire, and a diferent animation occurs.

Fifth. There needs to be something to differenciate between difficulty levels. Maybe a change in color scheme, some sort of animation, or something? i need for you to help me with some ideas and brainstorming that issue.
