
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/transformer_models.dart';
import '../state/transformer_state.dart';
import '../modes/guided_mode.dart';
import '../modes/quiz_mode.dart';

/// Main transformer trainer widget - entry point for the component
class TransformerTrainer extends StatelessWidget {
  final TransformerBankType initialBankType;
  final TrainingMode initialMode;
  final DifficultyLevel initialDifficulty;
  final Function(TrainingStep)? onStepComplete;
  final Function(TransformerBankType)? onBankComplete;
  final Function(String)? onError;

  const TransformerTrainer({
    Key? key,
    this.initialBankType = TransformerBankType.wyeToWye,
    this.initialMode = TrainingMode.guided,
    this.initialDifficulty = DifficultyLevel.beginner,
    this.onStepComplete,
    this.onBankComplete,
    this.onError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => TransformerTrainerState()
        ..updateState(TrainingState(
          bankType: initialBankType,
          mode: initialMode,
          difficulty: initialDifficulty,
        )),
      child: Consumer<TransformerTrainerState>(
        builder: (context, state, child) {
          return Scaffold(
            appBar: _buildAppBar(context, state),
            body: Column(
              children: [
                _buildModeToggle(context, state),
                _buildBankTypeSelector(context, state),
                _buildDifficultySelector(context, state),
                Expanded(
                  child: _buildMainContent(context, state),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Build app bar with title and reset button
  PreferredSizeWidget _buildAppBar(BuildContext context, TransformerTrainerState state) {
    return AppBar(
      title: const Text('Transformer Bank Trainer'),
      backgroundColor: Colors.indigo,
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => state.clearConnections(),
          tooltip: 'Reset Training',
        ),
        IconButton(
          icon: const Icon(Icons.help_outline),
          onPressed: () => _showHelpDialog(context),
          tooltip: 'Help',
        ),
      ],
    );
  }

  /// Build mode toggle buttons (Guided vs Quiz)
  Widget _buildModeToggle(BuildContext context, TransformerTrainerState state) {
    return Container(
      margin: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildToggleButton(
              context,
              'Guided Mode',
              TrainingMode.guided,
              state.currentState.mode,
              (mode) => state.setMode(mode),
              Icons.school,
            ),
          ),
          Expanded(
            child: _buildToggleButton(
              context,
              'Quiz Mode',
              TrainingMode.quiz,
              state.currentState.mode,
              (mode) => state.setMode(mode),
              Icons.quiz,
            ),
          ),
        ],
      ),
    );
  }

  /// Build bank type selector dropdown
  Widget _buildBankTypeSelector(BuildContext context, TransformerTrainerState state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: DropdownButton<TransformerBankType>(
        value: state.currentState.bankType,
        isExpanded: true,
        underline: const SizedBox(),
        items: TransformerBankType.values.map((type) {
          return DropdownMenuItem(
            value: type,
            child: Text(_getBankTypeDisplayName(type)),
          );
        }).toList(),
        onChanged: (type) {
          if (type != null) {
            state.setBankType(type);
          }
        },
      ),
    );
  }

  /// Build difficulty level selector
  Widget _buildDifficultySelector(BuildContext context, TransformerTrainerState state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          const Text('Difficulty: ', style: TextStyle(fontWeight: FontWeight.bold)),
          ...DifficultyLevel.values.map((level) => Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: _buildToggleButton(
                context,
                _getDifficultyDisplayName(level),
                level,
                state.currentState.difficulty,
                (diff) => state.setDifficulty(diff),
                _getDifficultyIcon(level),
              ),
            ),
          )),
        ],
      ),
    );
  }

  /// Build main content based on current mode
  Widget _buildMainContent(BuildContext context, TransformerTrainerState state) {
    switch (state.currentState.mode) {
      case TrainingMode.guided:
        return GuidedModeWidget(
          onStepComplete: onStepComplete,
          onBankComplete: onBankComplete,
          onError: onError,
        );
      case TrainingMode.quiz:
        return QuizModeWidget(
          onBankComplete: onBankComplete,
          onError: onError,
        );
    }
  }

  /// Build toggle button helper
  Widget _buildToggleButton<T>(
    BuildContext context,
    String label,
    T value,
    T currentValue,
    Function(T) onChanged,
    IconData icon,
  ) {
    final isSelected = value == currentValue;
    return GestureDetector(
      onTap: () => onChanged(value),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
        decoration: BoxDecoration(
          color: isSelected ? Colors.indigo : Colors.transparent,
          borderRadius: BorderRadius.circular(6.0),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey[600],
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show help dialog
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transformer Bank Trainer Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Guided Mode:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Follow step-by-step instructions\n• Get hints and explanations\n• Learn proper connection procedures\n'),
              Text('Quiz Mode:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Test your knowledge\n• Make connections without guidance\n• Receive feedback on completion\n'),
              Text('Making Connections:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Tap connection points to make wires\n• Drag to connect two points\n• Incorrect connections will flash red\n• Correct connections glow green'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }

  /// Get display name for bank type
  String _getBankTypeDisplayName(TransformerBankType type) {
    switch (type) {
      case TransformerBankType.wyeToWye:
        return 'Wye-Wye';
      case TransformerBankType.deltaToDelta:
        return 'Delta-Delta';
      case TransformerBankType.wyeToDelta:
        return 'Wye-Delta';
      case TransformerBankType.deltaToWye:
        return 'Delta-Wye';
      case TransformerBankType.openDelta:
        return 'Open Delta';
    }
  }

  /// Get display name for difficulty level
  String _getDifficultyDisplayName(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.beginner:
        return 'Beginner';
      case DifficultyLevel.intermediate:
        return 'Intermediate';
      case DifficultyLevel.advanced:
        return 'Advanced';
    }
  }

  /// Get icon for difficulty level
  IconData _getDifficultyIcon(DifficultyLevel level) {
    switch (level) {
      case DifficultyLevel.beginner:
        return Icons.looks_one;
      case DifficultyLevel.intermediate:
        return Icons.looks_two;
      case DifficultyLevel.advanced:
        return Icons.looks_3;
    }
  }
}
