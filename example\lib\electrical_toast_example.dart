import 'package:flutter/material.dart';
import 'package:journeyman_jobs/design_system/app_theme.dart';
import 'package:journeyman_jobs/design_system/components/jj_electrical_toast.dart';
import 'package:journeyman_jobs/design_system/components/reusable_components.dart';

/// Example screen demonstrating the JJElectricalToast component
class ElectricalToastExample extends StatelessWidget {
  const ElectricalToastExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Electrical Toast Examples'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0), // Replaced AppTheme.spacingLg
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'JJ Electrical Toast Component',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12.0), // Replaced AppTheme.spacingSm
            Text(
              'Tap the buttons below to see different toast styles with electrical theming',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32.0), // Replaced AppTheme.spacingXl

            // Success Toast
            JJPrimaryButton(
              text: 'Show Success Toast',
              icon: Icons.check_circle,
              onPressed: () {
                JJElectricalToast.showSuccess(
                  context: context,
                  message: 'Job application submitted successfully!',
                  actionLabel: 'View Jobs',
                  onActionPressed: () {
                    debugPrint('View Jobs tapped');
                  },
                );
              },
              isFullWidth: true,
            ),
            const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd

            // Error Toast
            JJSecondaryButton(
              text: 'Show Error Toast',
              icon: Icons.error,
              onPressed: () {
                JJElectricalToast.showError(
                  context: context,
                  message: 'Connection to job board failed. Please check your network.',
                  actionLabel: 'Retry',
                  onActionPressed: () {
                    debugPrint('Retry tapped');
                  },
                );
              },
              isFullWidth: true,
            ),
            const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd

            // Warning Toast
            JJPrimaryButton(
              text: 'Show Warning Toast',
              icon: Icons.warning,
              onPressed: () {
                JJElectricalToast.showWarning(
                  context: context,
                  message: 'Storm work alert: High voltage conditions detected',
                  duration: const Duration(seconds: 6),
                );
              },
              isFullWidth: true,
            ),
            const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd

            // Info Toast
            JJSecondaryButton(
              text: 'Show Info Toast',
              icon: Icons.info,
              onPressed: () {
                JJElectricalToast.showInfo(
                  context: context,
                  message: 'New jobs available in your area',
                  actionLabel: 'Browse',
                  onActionPressed: () {
                    debugPrint('Browse tapped');
                  },
                );
              },
              isFullWidth: true,
            ),
            const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd

            // Power Toast (Custom electrical theme)
            Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange, Colors.deepOrange],
                ), // Replaced AppTheme.buttonGradient
                borderRadius: BorderRadius.circular(8.0), // Replaced AppTheme.radiusMd
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  )
                ], // Replaced AppTheme.shadowSm
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    JJElectricalToast.showPower(
                      context: context,
                      message: 'Power grid status: All systems operational',
                      duration: const Duration(seconds: 5),
                    );
                  },
                  borderRadius: BorderRadius.circular(8.0), // Replaced AppTheme.radiusMd
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.flash_on,
                        color: Colors.white, // Replaced AppTheme.white
                        size: 20.0, // Replaced AppTheme.iconSm
                      ),
                      const SizedBox(width: 12.0), // Replaced AppTheme.spacingSm
                      Text(
                        'Show Power Toast',
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: Colors.white, // Replaced AppTheme.white
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16.0), // Replaced AppTheme.spacingMd

            // Custom Toast with Custom Icon
            JJSecondaryButton(
              text: 'Show Custom Toast',
              icon: Icons.build,
              onPressed: () {
                JJElectricalToast.showCustom(
                  context: context,
                  message: 'Maintenance scheduled for tonight at 11 PM',
                  icon: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.amber.withOpacity(0.2), // Replaced AppTheme.warningYellow
                      borderRadius: BorderRadius.circular(4.0), // Replaced AppTheme.radiusSm
                    ),
                    child: const Icon(
                      Icons.build_circle,
                      size: 20.0, // Replaced AppTheme.iconSm
                      color: Colors.amber, // Replaced AppTheme.warningYellow
                    ),
                  ),
                  type: JJToastType.warning,
                  actionLabel: 'Schedule',
                  onActionPressed: () {
                    debugPrint('Schedule tapped');
                  },
                );
              },
              isFullWidth: true,
            ),

            const Spacer(),

            // Tips section
            Container(
              padding: const EdgeInsets.all(16.0), // Replaced AppTheme.spacingMd
              decoration: BoxDecoration(
                color: Colors.grey.shade50, // Replaced AppTheme.offWhite
                borderRadius: BorderRadius.circular(8.0), // Replaced AppTheme.radiusMd
                border: Border.all(color: Colors.grey.shade300), // Replaced AppTheme.lightGray
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Toast Features:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 12.0), // Replaced AppTheme.spacingSm
                  Text(
                    '• Electrical-themed animations and styling\n'
                    '• Progress indicator showing remaining time\n'
                    '• Swipe up to dismiss early\n'
                    '• Tap to dismiss\n'
                    '• Optional action buttons\n'
                    '• Custom icons and electrical illustrations',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}