
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/transformer_models.dart';
import '../state/transformer_state.dart';
import '../widgets/transformer_diagram.dart';
import '../models/educational_content.dart';

/// Guided training mode with step-by-step instructions
class GuidedModeWidget extends StatelessWidget {
  final Function(TrainingStep)? onStepComplete;
  final Function(TransformerBankType)? onBankComplete;
  final Function(String)? onError;

  const GuidedModeWidget({
    Key? key,
    this.onStepComplete,
    this.onBankComplete,
    this.onError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<TransformerTrainerState>(
      builder: (context, state, child) {
        final currentStep = state.currentStep;
        
        return Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(state),
            
            // Current step instructions (if available)
            if (currentStep != null) _buildStepInstructions(currentStep),
            
            // Bank information panel
            _buildBankInfoPanel(state),
            
            // Main transformer diagram
            Expanded(
              child: TransformerDiagram(
                onConnectionMade: (fromId, toId) {
                  state.addConnection(fromId, toId);
                  
                  // Check if step was completed
                  final updatedStep = state.currentStep;
                  if (updatedStep != currentStep && onStepComplete != null) {
                    onStepComplete!(currentStep);
                  }
                  
                  // Check if bank was completed
                  if (state.currentState.isComplete && onBankComplete != null) {
                    onBankComplete!(state.currentState.bankType);
                  }
                },
                onConnectionError: (error) {
                  if (onError != null) {
                    onError!(error);
                  }
                },
              ),
            ),
            
            // Step navigation buttons
            _buildNavigationButtons(context, state),
          ],
        );
      },
    );
  }

  /// Build progress indicator showing current step
  Widget _buildProgressIndicator(TransformerTrainerState state) {
    final steps = state.trainingSteps;
    if (steps.isEmpty) return const SizedBox();
    
    final currentStepIndex = state.currentState.currentStep;
    final progress = currentStepIndex / steps.length;
    
    return Container(
      margin: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Text(
            'Step ${currentStepIndex + 1} of ${steps.length}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.indigo),
          ),
        ],
      ),
    );
  }

  /// Build current step instructions
  Widget _buildStepInstructions(TrainingStep step) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: Colors.blue[600]),
              const SizedBox(width: 8),
              const Text(
                'Current Step',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            step.instruction,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Text(
            step.explanation,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          
          // Safety note if present
          if (step.safetyNote != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(6.0),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.warning_amber, color: Colors.orange[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Safety Note:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(step.safetyNote!),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Common mistake warning if present
          if (step.commonMistake != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(6.0),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.error_outline, color: Colors.red[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Common Mistake:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(step.commonMistake!),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build bank information panel
  Widget _buildBankInfoPanel(TransformerTrainerState state) {
    final bankType = state.currentState.bankType;
    final title = EducationalContent.getBankTitle(bankType);
    final description = EducationalContent.getBankDescription(bankType);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: ExpansionTile(
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          description,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(description),
                const SizedBox(height: 12),
                
                // Safety notes
                const Text(
                  'Safety Considerations:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...EducationalContent.getSafetyNotes(bankType).map(
                  (note) => Padding(
                    padding: const EdgeInsets.only(top: 4.0, left: 16.0),
                    child: Text('• $note'),
                  ),
                ),
                const SizedBox(height: 12),
                
                // Common mistakes
                const Text(
                  'Common Mistakes to Avoid:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...EducationalContent.getCommonMistakes(bankType).map(
                  (mistake) => Padding(
                    padding: const EdgeInsets.only(top: 4.0, left: 16.0),
                    child: Text('• $mistake'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build navigation buttons for step control
  Widget _buildNavigationButtons(BuildContext context, TransformerTrainerState state) {
    final steps = state.trainingSteps;
    final currentStepIndex = state.currentState.currentStep;
    final isComplete = state.currentState.isComplete;
    
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Previous step button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: currentStepIndex > 0
                  ? () {
                      final newState = state.currentState.copyWith(
                        currentStep: currentStepIndex - 1,
                      );
                      state.updateState(newState);
                    }
                  : null,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Previous'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[300],
                foregroundColor: Colors.grey[700],
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Next step / Complete button
          Expanded(
            child: ElevatedButton.icon(
              onPressed: isComplete
                  ? () {
                      _showCompletionDialog(context, state.currentState.bankType);
                    }
                  : (currentStepIndex < steps.length - 1)
                      ? () {
                          final newState = state.currentState.copyWith(
                            currentStep: currentStepIndex + 1,
                          );
                          state.updateState(newState);
                        }
                      : null,
              icon: Icon(isComplete ? Icons.check_circle : Icons.arrow_forward),
              label: Text(isComplete ? 'Complete!' : 'Next'),
              style: ElevatedButton.styleFrom(
                backgroundColor: isComplete ? Colors.green : Colors.indigo,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show completion dialog
  void _showCompletionDialog(BuildContext context, TransformerBankType bankType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.celebration, color: Colors.green),
            SizedBox(width: 8),
            Text('Congratulations!'),
          ],
        ),
        content: Text(
          'You have successfully completed the ${EducationalContent.getBankTitle(bankType)} training!',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Clear connections to start fresh
              Provider.of<TransformerTrainerState>(context, listen: false)
                  .clearConnections();
            },
            child: const Text('Start Over'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }
}
