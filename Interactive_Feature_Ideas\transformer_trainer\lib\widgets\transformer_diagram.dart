
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/transformer_models.dart';
import '../state/transformer_state.dart';
import '../painters/base_transformer_painter.dart';
import '../painters/wye_wye_painter.dart';
import '../painters/delta_delta_painter.dart';
import '../painters/wye_delta_painter.dart';
import '../painters/delta_wye_painter.dart';
import '../painters/open_delta_painter.dart';
import '../widgets/connection_point.dart';
import '../animations/flash_animation.dart';

/// Interactive transformer diagram widget
class TransformerDiagram extends StatefulWidget {
  final Function(String fromId, String toId) onConnectionMade;
  final Function(String error) onConnectionError;
  final bool showGuidance;

  const TransformerDiagram({
    Key? key,
    required this.onConnectionMade,
    required this.onConnectionError,
    this.showGuidance = true,
  }) : super(key: key);

  @override
  State<TransformerDiagram> createState() => _TransformerDiagramState();
}

class _TransformerDiagramState extends State<TransformerDiagram>
    with TickerProviderStateMixin {
  String? selectedConnectionId;
  AnimationController? flashAnimationController;
  AnimationController? successAnimationController;
  
  @override
  void initState() {
    super.initState();
    flashAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }

  @override
  void dispose() {
    flashAnimationController?.dispose();
    successAnimationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransformerTrainerState>(
      builder: (context, state, child) {
        return Container(
          padding: const EdgeInsets.all(16.0),
          child: Stack(
            children: [
              // Background transformer diagram
              Positioned.fill(
                child: CustomPaint(
                  painter: _getPainter(state.currentState.bankType),
                ),
              ),
              
              // Interactive connection points
              ..._buildConnectionPoints(state),
              
              // Existing wire connections
              ..._buildExistingConnections(state),
              
              // Flash animation overlay (for errors)
              if (flashAnimationController?.isAnimating == true)
                FlashAnimationWidget(
                  controller: flashAnimationController!,
                ),
            ],
          ),
        );
      },
    );
  }

  /// Get appropriate painter for the current bank type
  BaseTransformerPainter _getPainter(TransformerBankType bankType) {
    switch (bankType) {
      case TransformerBankType.wyeToWye:
        return WyeWyePainter();
      case TransformerBankType.deltaToDelta:
        return DeltaDeltaPainter();
      case TransformerBankType.wyeToDelta:
        return WyeDeltaPainter();
      case TransformerBankType.deltaToWye:
        return DeltaWyePainter();
      case TransformerBankType.openDelta:
        return OpenDeltaPainter();
    }
  }

  /// Build interactive connection points
  List<Widget> _buildConnectionPoints(TransformerTrainerState state) {
    return state.connectionPoints.map((point) {
      final isSelected = selectedConnectionId == point.id;
      final isConnected = state.currentState.connections
          .any((conn) => conn.fromPointId == point.id || conn.toPointId == point.id);
      
      return Positioned(
        left: point.position.dx - 12,
        top: point.position.dy - 12,
        child: ConnectionPointWidget(
          connectionPoint: point,
          isSelected: isSelected,
          isConnected: isConnected,
          showGuidance: widget.showGuidance,
          onTap: () => _onConnectionPointTapped(point.id, state),
        ),
      );
    }).toList();
  }

  /// Build visual representation of existing connections
  List<Widget> _buildExistingConnections(TransformerTrainerState state) {
    return state.currentState.connections.map((connection) {
      final fromPoint = state.connectionPoints
          .firstWhere((p) => p.id == connection.fromPointId);
      final toPoint = state.connectionPoints
          .firstWhere((p) => p.id == connection.toPointId);
      
      return Positioned.fill(
        child: CustomPaint(
          painter: ConnectionWirePainter(
            from: fromPoint.position,
            to: toPoint.position,
            isCorrect: connection.isCorrect,
          ),
        ),
      );
    }).toList();
  }

  /// Handle connection point tap
  void _onConnectionPointTapped(String pointId, TransformerTrainerState state) {
    if (selectedConnectionId == null) {
      // First point selected
      setState(() {
        selectedConnectionId = pointId;
      });
    } else if (selectedConnectionId == pointId) {
      // Same point tapped - deselect
      setState(() {
        selectedConnectionId = null;
      });
    } else {
      // Second point selected - make connection
      final fromId = selectedConnectionId!;
      final toId = pointId;
      
      setState(() {
        selectedConnectionId = null;
      });
      
      // Check if connection is valid
      if (_isValidConnection(fromId, toId, state)) {
        widget.onConnectionMade(fromId, toId);
        successAnimationController?.forward().then((_) {
          successAnimationController?.reset();
        });
      } else {
        widget.onConnectionError('Invalid connection: $fromId to $toId');
        flashAnimationController?.forward().then((_) {
          flashAnimationController?.reset();
        });
      }
    }
  }

  /// Check if a connection between two points is valid
  bool _isValidConnection(String fromId, String toId, TransformerTrainerState state) {
    // Check if connection already exists
    final existingConnection = state.currentState.connections
        .any((conn) => 
            (conn.fromPointId == fromId && conn.toPointId == toId) ||
            (conn.fromPointId == toId && conn.toPointId == fromId));
    
    if (existingConnection) return false;
    
    // Check against required connections
    return state.requiredConnections.any((req) =>
        (req.fromPointId == fromId && req.toPointId == toId) ||
        (req.fromPointId == toId && req.toPointId == fromId));
  }
}

/// Custom painter for drawing wire connections
class ConnectionWirePainter extends CustomPainter {
  final Offset from;
  final Offset to;
  final bool isCorrect;

  ConnectionWirePainter({
    required this.from,
    required this.to,
    required this.isCorrect,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    if (isCorrect) {
      paint.color = Colors.green;
    } else {
      paint.color = Colors.red;
      paint.strokeWidth = 4.0;
    }

    // Draw the wire connection
    canvas.drawLine(from, to, paint);

    // Add arrowhead to indicate direction
    _drawArrowHead(canvas, from, to, paint);
  }

  void _drawArrowHead(Canvas canvas, Offset from, Offset to, Paint paint) {
    const arrowSize = 8.0;
    final direction = (to - from).direction;
    
    final arrowPoint1 = Offset(
      to.dx - arrowSize * 0.866 * (direction - 0.5).cos,
      to.dy - arrowSize * 0.866 * (direction - 0.5).sin,
    );
    
    final arrowPoint2 = Offset(
      to.dx - arrowSize * 0.866 * (direction + 0.5).cos,
      to.dy - arrowSize * 0.866 * (direction + 0.5).sin,
    );

    final path = Path()
      ..moveTo(to.dx, to.dy)
      ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
      ..moveTo(to.dx, to.dy)
      ..lineTo(arrowPoint2.dx, arrowPoint2.dy);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(ConnectionWirePainter oldDelegate) {
    return from != oldDelegate.from ||
           to != oldDelegate.to ||
           isCorrect != oldDelegate.isCorrect;
  }
}

extension on Offset {
  double get direction => math.atan2(dy, dx);
}

// Add missing import
import 'dart:math' as math;
